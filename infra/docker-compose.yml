networks:
  proxy:
    external: true
    name: proxy
    driver: bridge

volumes:
  # portainer_data:
  # grafana_data:
  # prometheus_data:
  mariadb-data:

services:
  traefik:
    container_name: traefik
    image: traefik:v3.5.2
    command:
      - --api.dashboard=true # ⚠️ Solo en local
      - --api.insecure=true # ⚠️ Solo en local
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --providers.docker.network=proxy
      - --providers.file.directory=/etc/traefik/dynamic
      - --providers.file.watch=true
      - --entryPoints.web.address=:80
      - --entryPoints.websecure.address=:443
      - --entryPoints.websecure.http.tls=true
      # redirección http -> https
      - --entryPoints.web.http.redirections.entryPoint.to=websecure
      - --entryPoints.web.http.redirections.entryPoint.scheme=https
      # logs
      - --accesslog=true
      - --accesslog.filepath=/var/log/traefik/access.log
      - --log.level=INFO
    restart: unless-stopped
    security_opt:
      - no-new-privileges:true
    ports:
      - 80:80
      - 443:443
      - 8080:8080
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./certs:/certs:ro
      - ./dynamic:/etc/traefik/dynamic:ro
    labels:
      - traefik.enable=true
      - traefik.http.routers.traefik.rule=Host(`traefik.msarknet.me`)
      - traefik.http.routers.traefik.entrypoints=websecure
      - traefik.http.routers.traefik.service=api@internal
      - traefik.http.routers.traefik.tls=true
      - traefik.http.routers.traefik.middlewares=secure-headers@file,auth@file
    networks:
      - proxy

  # portainer:
  #   container_name: portainer
  #   image: portainer/portainer-ce:latest
  #   restart: unless-stopped
  #   volumes:
  #     - /var/run/docker.sock:/var/run/docker.sock:ro
  #     - portainer_data:/data
  #   labels:
  #     - traefik.enable=true
  #     - traefik.http.routers.portainer.rule=Host(`portainer.msarknet.me`)
  #     - traefik.http.routers.portainer.entrypoints=websecure
  #     - traefik.http.routers.portainer.tls=true
  #     - traefik.http.routers.portainer.middlewares=secure-headers@file
  #     - traefik.http.services.portainer.loadbalancer.server.port=9000
  #   networks:
  #     - proxy

  # whoami:
  #   container_name: whoami
  #   image: traefik/whoami
  #   restart: unless-stopped
  #   labels:
  #     - traefik.enable=true
  #     - traefik.http.routers.whoami.rule=Host(`whoami.msarknet.me`)
  #     - traefik.http.routers.whoami.entrypoints=websecure
  #     - traefik.http.routers.whoami.tls=true
  #     - traefik.http.routers.whoami.middlewares=secure-headers@file,cors-headers@file,rate-limit@file
  #   networks:
  #     - proxy

  # grafana:
  #   container_name: grafana
  #   image: grafana/grafana:latest
  #   restart: unless-stopped
  #   environment:
  #     - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER}
  #     - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
  #     - GF_SECURITY_ADMIN_EMAIL=${GRAFANA_ADMIN_EMAIL}
  #     - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
  #   volumes:
  #     - grafana_data:/var/lib/grafana
  #   labels:
  #     - traefik.enable=true
  #     - traefik.http.routers.grafana.rule=Host(`grafana.msarknet.me`)
  #     - traefik.http.routers.grafana.entrypoints=websecure
  #     - traefik.http.routers.grafana.tls=true
  #     - traefik.http.routers.grafana.middlewares=secure-headers@file
  #     - traefik.http.services.grafana.loadbalancer.server.port=3000
  #   networks:
  #     - proxy

  # prometheus:
  #   container_name: prometheus
  #   image: prom/prometheus:latest
  #   restart: unless-stopped
  #   networks:
  #     - proxy
  #   volumes:
  #     - prometheus_data:/prometheus
  #     - ./dynamic/prometheus.yml:/etc/prometheus/prometheus.yml:ro
  #   command:
  #     - --config.file=/etc/prometheus/prometheus.yml
  #     - --storage.tsdb.path=/prometheus
  #     - --web.console.libraries=/etc/prometheus/console_libraries
  #     - --web.console.templates=/etc/prometheus/consoles
  #     - --storage.tsdb.retention.time=200h
  #     - --web.enable-lifecycle
  #     - --web.enable-admin-api
  #   labels:
  #     - traefik.enable=true
  #     - traefik.http.routers.prometheus.rule=Host(`prom.msarknet.me`)
  #     - traefik.http.routers.prometheus.entrypoints=websecure
  #     - traefik.http.routers.prometheus.tls=true
  #     - traefik.http.routers.prometheus.middlewares=secure-headers@file,auth@file
  #     - traefik.http.services.prometheus.loadbalancer.server.port=9090

  mariadb:
    container_name: mariadb
    image: mariadb:12.0.2
    restart: unless-stopped
    environment:
      MARIADB_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MARIADB_DATABASE: ${DB_DATABASE}
      MARIADB_USER: ${DB_USER}
      MARIADB_PASSWORD: ${DB_PASSWORD}
    volumes:
      - mariadb-data:/var/lib/mysql
    networks:
      - proxy

  adminer:
    container_name: adminer
    image: adminer:latest
    restart: unless-stopped
    environment:
      ADMINER_DEFAULT_SERVER: ${DB_HOST}
    labels:
      - traefik.enable=true
      - traefik.http.routers.adminer.rule=Host(`adminer.msarknet.me`)
      - traefik.http.routers.adminer.entrypoints=websecure
      - traefik.http.routers.adminer.tls=true
      - traefik.http.routers.adminer.middlewares=secure-headers@file,auth@file
      - traefik.http.services.adminer.loadbalancer.server.port=8080
    networks:
      - proxy
    depends_on:
      - mariadb

  # ===============================
  # APPS
  # ===============================
  cerebro-fe:
    container_name: cerebro-fe
    build:
      context: ../cerebro-fe
      dockerfile: Dockerfile
      target: development
    restart: unless-stopped
    volumes:
      - ../cerebro-fe:/app
      - ../cerebro-fe/node_modules:/app/node_modules
    labels:
      - traefik.enable=true
      - traefik.http.routers.cerebro-fe.rule=Host(`msarknet.me`)
      - traefik.http.routers.cerebro-fe.entrypoints=websecure
      - traefik.http.routers.cerebro-fe.tls=true
      - traefik.http.routers.cerebro-fe.middlewares=spa-headers@file
      - traefik.http.services.cerebro-fe.loadbalancer.server.port=5173
    networks:
      - proxy

  cerebro-be:
    container_name: cerebro-be
    build:
      context: ../cerebro-be
      dockerfile: Dockerfile
      target: development
    restart: unless-stopped
    volumes:
      - ../cerebro-be:/app
      - ../cerebro-be/node_modules:/app/node_modules
      - /var/run/docker.sock:/var/run/docker.sock
    labels:
      - traefik.enable=true
      - traefik.http.routers.cerebro-be.rule=Host(`api.msarknet.me`)
      - traefik.http.routers.cerebro-be.entrypoints=websecure
      - traefik.http.routers.cerebro-be.tls=true
      - traefik.http.routers.cerebro-be.middlewares=secure-headers@file
      - traefik.http.services.cerebro-be.loadbalancer.server.port=5000
    networks:
      - proxy

  docs:
    container_name: docs-service
    image: nginx:alpine
    restart: unless-stopped
    networks:
      - proxy
    volumes:
      - ./web-content/docs:/usr/share/nginx/html:ro
    labels:
      - traefik.enable=true
      - traefik.http.routers.docs.rule=Host(`docs.msarknet.me`)
      - traefik.http.routers.docs.entrypoints=websecure
      - traefik.http.routers.docs.tls=true
      - traefik.http.routers.docs.middlewares=secure-headers@file,compression@file
